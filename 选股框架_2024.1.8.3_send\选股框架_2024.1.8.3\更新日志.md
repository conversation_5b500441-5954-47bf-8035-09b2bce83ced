# 【2024年09月10日】选股策略_2024.1.8.3

更新内容： 1、修复分域分析工具无法选出top行业的bug，影响范围：DomainAnalysis.py
2、修复择时函数输出最新的结果signal为空的bug，影响范围：2_选股.py 3、统一单因子 & 双因子的因子分析逻辑，影响范围：Tool.tFunctions.py 更新建议： 本次更新内较少，直接替换相关因子文件即可

# 【2024年08月21日】选股策略_2024.1.8.2

更新内容： 1、更新1_因子分析.py脚本，支持读取多个因子文件，影响范围：Tools.1_因子分析.py 2、新增双因子分析工具，影响范围：Tools.1_因子分析_双因子.py &
Tools.utils.PlotFunctions.py & Tools.utils.tFunctions.py 3、将策略查看器中的绝对路径更改为相对路径，影响范围：Tools.2_策略查看器.py 4、增加清理日志的逻辑，影响范围：1_
选股数据整理.py & Functions.py 5、修复选股结果中，历年最多选股结果的可能存在的bug，影响范围：Functions.py 6、增加中证2000成分股的填充，影响范围：Functions.py 更新建议：
本次更新内较少，直接替换相关因子文件即可

# 【2024年07月23日】选股策略_2024.1.8.1

更新内容： 1、风格因子存在笔误，已修正，影响范围：风格因子.py 2、保存待分析数据时，增加了该数据是否择时开仓的情况，影响范围：2_选股 3、在策略查看器中，增加对空仓的处理，影响范围：Tools.2_策略查看器

# 【2024年07月11日】选股策略_2024.1.8.0

更新内容： 1、合并【研发中的选股策略】 & 【实盘中的选股策略】，策略放在【选股策略】文件夹中 2、删除4号脚本，将其内容和3号脚本合并
3、删除增量模式，新增实盘模式trading_mode，详细信息见Config中的注释，影响范围：1_选股数据整理 & Functions
4、脚本1增加计算因子时的数据长度判断，减少大家在开发因子时因为使用了dropna之类的操作带来的数据问题 5、脚本2增加交易日期的判断，最大程度避长度匹配的报错(删除数据检查的流程)，影响范围：2_选股 & Functions
6、脚本2支持多种手续费的日内换仓计算，配置与使用说明详见Config中的注释，影响范围：2_选股 & Functions & Config
7、脚本2增加策略输出的信息，将待分析数据格式从csv更改为pkl，支持后续轮动的升级，影响范围：2_选股 & 2_策略查看器 8、脚本2增加空仓信息提示，影响范围：2_选股 & Functions
9、Config删除run_all_offset，引入功能类似的trading_mode，并引入相关逻辑代码，影响范围：Config 10、Config删除subset_num、stg_folder，影响范围：Config
11、将DomainAnalysis.py加入program中，并且修改其IC计算方式，与因子分析保持一致 12、Evaluate.py文件保存路径，影响范围：Evaluate 13、Functions.py优化指数导入函数
14、Functions.py引入增加其他时间点换仓的必要涨跌幅计算 15、Functions.py增加对科创板sz31的支持 16、Functions.py修复退市股票依旧会存在数据中的bug，此条修改可能会影响截面因子的计算
17、删除策略文件中针对filter_stock函数针对sz300156和sz300362的特殊处理（已经在原始数据中修正） 18、删除策略文件中对总市值因子的引用，因为基础数据中有，不需要额外引用
19、A股溢价率.py优化路径导入，将加载港股数据的代码单独抽成load函数 20、邢不行资金流.py增加路径容错处理 21、因子分析优化修复了风格因子计算时的bug 22、策略查看器做了适配性的优化
23、修改了选股数据整理流程图.xlsx文件 24、因子分析中增加data_process函数，允许对数据进行预处理，例如：过滤、计算复合因子等等 25、调整输出的图片尺寸，并且增加历年最多的选股表格

更新建议： 本次更新内容较多，建议对比后替换整个项目。 本次更新后，需要配合轮动2024.1.8.0使用，实盘框架也需要为2024.1.4.0版本

# 【2024年06月05日】选股策略_2024.1.7.5

更新内容： 1.修复A股溢价率因子与其他因子一块计算时可能导致的数据缺失问题。影响：program.因子.A股溢价率.py 2.优化0_尾盘获取数据.py，更新非交易日不运行的功能。影响：program.0_尾盘获取数据.py
本次更新建议替换A股溢价率因子与0号脚本。

# 【2024年05月31日】选股策略_2024.1.7.4

更新内容： 1.修复A股溢价率A股开盘但港股不开盘时导致计算因子为nan的错误；修正计算A股和港股涨跌幅时未考虑复权的错误，影响：program.因子.A股溢价率.py 本次更新建议替换A股溢价率因子。

# 【2024年05月23日】选股策略_2024.1.7.3

更新内容： 1.修复Rainbow.py中的一些bug，影响：program.Rainbow.py 2.更新安装依赖环境的py脚本，影响：安装依赖环境.py
3.新增笛卡尔2策略，策略直播参考：24分享会策略直播8-笛卡尔2选股策略，影响：program.研究中的选股策略.笛卡尔2.py、program.因子.A股溢价率.py
本次更新建议直接替换上述两个文件，并把笛卡尔2策略和A股溢价率因子移动到本地框架中。

# 【2024年05月9日】选股策略_2024.1.7.2

更新内容： 1.优化了Functions.check_data函数的逻辑 2.修复因子分析多offset会报错的bug 3.因子分析中加入截面因子 4.加入针对period_offset文件的检查，没及时更新会有提示
5.脚本0加入非交易日不更新的逻辑 6.优化Functions.get_data_path函数的逻辑 7.优化Evaluate.merge_html函数的逻辑 8.优化脚本0更新指数的逻辑 9.修复调用脚本2进行遍历的逻辑
10.引入脚本4，专门用于参数遍历 11.增加记录日志和错题集的功能 12.优化AH溢价率的计算 本次更新较多，建议对比后替换整个项目。

# 【2024年03月12日】选股策略_2024.1.7.1

更新内容： 在框架中加入更多的子策略 优化AH溢价率计算，解决港股开盘但是A股不开盘时造成的溢价计算偏差。 建议将【研究中的选股策略】以及【因子】文件夹中的内容全部覆盖

# 【2024年03月12日】选股策略_2024.1.7

更新内容： 引入主文件夹概念，整个项目中只需要配置主文件夹的路径即可 详见：https://bbs.quantclass.cn/thread/39599
变更内容： 1、Functions.py 2、Config.py 3、因子.A股溢价率.py 4、因子.邢不行资金流.py

# 【2024年03月02日】选股策略_2024.1.6

更新内容： 1、修改 2_选股.py 中参数遍历时的bug 2、修改 Functions.save_select_result 中的bug 3、修复 Functions.factor_neutralization函数的一些bug 4、优化
Tools.2_策略查看器.py 5、优化 Function_fin.merge_with_finance_data函数，无财务数据时将会提示 6、修复选股数据整理时计算增量数据的bug 7、优化因子分析相关代码
8、在选股中加入数据检查，最大限度避免数据不一致导致报错 9、修复总市值因子不能做因子分析的bug 10、优化稳健性测试的函数，减少非必要的报错。 11、取消requirements文件，改为安装依赖环境.py，运行即可安装所有环境

更新建议：本次更新内容较多，建议对比后，将代码进行整体替换

# 【2024年01月31日】选股策略_2024.1.5

更新内容： 1、修改Config中，尾盘模式的buy method 2、修改 3_遍历选股.py，使之兼容 生成交易计划

# 【2024年01月22日】选股策略_2024.1.4

更新内容： 1、在Function.save_back_test_result函数中删除累计涨跌字段 2、在Evaluate.merge_html修改了创建路径的错误 3、回测中增加尾盘换仓，且尾盘换仓只调整有差异的股票，影响范围：2_选股
& Functions（修复上次更新的bug） 4、在Function.save_select_result中加入空仓的提示

# 【2024年01月16日】选股策略_2024.1.3

更新内容： 1、计算下日状态的时候，修复end_exchange模式下的bug，影响范围：1_选股数据整理 & Functions 2、回滚数据时，指数未指定encoding，影响范围：0_尾盘获取数据
3、回测中增加尾盘换仓，且尾盘换仓只调整有差异的股票，影响范围：2_选股 & Functions

# 【2024年01月16日】选股策略_2024.1.2

更新内容： 1、【Functions.get_run_info】函数再run all offset模式下的bug修复 2、修复【实盘中的选股策略.小市值策略】未import pandas 3、修改化了脚本3中，stg_file =
get_file_in_folder的路径 4、1_选股数据整理.pyj脚本调用0_尾盘获取数据.py的bug修复

# 【2024年01月12日】选股策略_2024.1.1发布