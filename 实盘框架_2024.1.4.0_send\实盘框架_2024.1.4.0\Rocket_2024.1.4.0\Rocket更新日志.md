# 【2024年07月13日】Rocket_2024.1.4.0
更新内容：
1、引入错题集功能，将原有的common.py文件删除，其相关内容完全由Rainbow.py替换。
2、将所有的 %s 占位符更改为 f'{}' 占位符
3、修复twap函数9点30之前不下单的bug
4、修复卖出订单部分卖出时可能存在问题
5、修改持仓对比时，若本地有持仓，但线上无持仓的情况，目前此类情况需要用户手动介入。
6、增加相同股票下单的限制，避免触发交易所风控，详见：config.py的order_limit参数
7、在save_hold、save_buy、save_sell函数中增加比对功能，只记录发生变更的数据
8、权重计算时，若存在选股数量是0（任意选股数量），将无法进入严格模式
9、计算下单金额时，若获取到可用资金是nan，会暂停执行，等待用户手动介入
10、加载交易计划时，增加空仓提示的过滤，股票代码为empty的股票不会被加载
11、下单逻辑中增加-2的返回值，表示终止这笔交易，例如下单次数超出order_limit限制就会终止交易（影响所有交易函数）
更新建议：
本次更新内容较多，建议覆盖
本次更新后仅支持【选股框架2024.1.8.0】 、 【轮动框架2024.1.8.0】 、 【生成交易计划2024.1.4.0】及以上的版本

# 【2024年04月08日】Rocket_2024.1.3.2
1、本次版本更迭只更新了生成交易计划框架，Rocket可以忽略。

# 【2024年04月08日】Rocket_2024.1.3.1
1、修复日内换仓的一些bug，影响：exchange_api.py
2、修复订单同步的bug，影响：exchange_api.py
3、修复机器人发送消息的bug，影响:Common.py
更新建议：替换exchange_api.py、替换Common.py

# 【2024年04月07日】Rocket_2024.1.3.0
1、在task中加入休息时间，当前所有函数在午休期间不运行
2、修复持仓为空时，框架的报错
3、解决未查询到订单时一直撤单的bug
4、优化逆回购逻辑，自动在上交所和深交所中选择收益高的购买，且收益太低不买
5、tracking buy 引入参数
6、新增【更新行情源文件.py】，一键帮助更新行情源
7、增加非交易日运行程序的提示
8、企微机器人兼容两种不同的API写法
9、修复一些日内换仓的bug，及一些细节处理
10、增加console_check输入y后的提示
11、兼容一台电脑使用多个QMT的相关配置
12、优化twap逻辑，当前每次下单以金额来拆单，且最后一笔订单会重报
13、加入北交所交易规则：100起，往后1股起
14、常规日志改成每30s记录一次，减少日志量
15、优化订查询的记录逻辑
16、优化base buy购买涨停股票时，会不断撤单的bug
17、优化base sell卖出跌停股票时，会不断撤单的bug
18、修复其他交易函数中，涨跌停不恰当的撤单逻辑
19、新增【安装依赖环境V1.py】
20、修改【Rocket食用指南240407.pdf】
更新建议：
1、将之前实盘框架中的【data】文件夹复制过来，代码一律使用新的代码
2、【config.py】中的order_exe_path、stg_infos、account、robot_api、period_offset_path、stg_infos可以用拷贝过来，其他的不建议修改

# 【2024年02月19日】Rocket_2024.1.2
1、修改user_task.py中的load_trade_plan函数
2、修改functions.py中的cal_allocation_of_cap函数

# 【2024年02月04日】Rocket_2024.1.1
1、修改trade_calendar.py中之前debug留下的痕迹
2、完善食用指南的部分章节

# 【2024年01月31日】Rocket_2024.1.0
发布2024年全新版本